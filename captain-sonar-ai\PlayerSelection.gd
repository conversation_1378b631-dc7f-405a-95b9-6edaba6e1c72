extends Control

# Player Selection Screen - Station-Based Crew Assignment
# Allows assignment of Human players or AI to each submarine station

@onready var start_game_button = $ScrollContainer/VBoxContainer/StartGameButton
@onready var back_button = $BackButton
@onready var status_label = $ScrollContainer/VBoxContainer/StatusLabel

# Station types
enum StationType {
	RADIO,
	CAPTAIN,
	ENGINEER,
	WEAPONS
}

# Assignment types
enum AssignmentType {
	HUMAN,
	EASY_AI,
	NORMAL_AI,
	HARD_AI
}

# Store assignments for each station on each submarine
var player_assignments: Dictionary = {}
var enemy_assignments: Dictionary = {}

# Track which station the human player will control
var human_controlled_station: StationType = StationType.RADIO
var human_station_assigned: bool = false

func _ready() -> void:
	_initialize_assignments()
	_setup_button_connections()
	_update_ui()

func _initialize_assignments() -> void:
	# Initialize all stations to Easy AI by default (matching scene button_pressed = true)
	for station in StationType.values():
		player_assignments[station] = AssignmentType.EASY_AI
		enemy_assignments[station] = AssignmentType.EASY_AI

func _setup_button_connections() -> void:
	# Connect player submarine station buttons
	_connect_station_buttons("Radio", StationType.RADIO, true)
	_connect_station_buttons("Captain", StationType.CAPTAIN, true)
	_connect_station_buttons("Engineer", StationType.ENGINEER, true)
	_connect_station_buttons("Weapons", StationType.WEAPONS, true)
	
	# Connect enemy submarine station buttons
	_connect_station_buttons("Radio", StationType.RADIO, false)
	_connect_station_buttons("Captain", StationType.CAPTAIN, false)
	_connect_station_buttons("Engineer", StationType.ENGINEER, false)
	_connect_station_buttons("Weapons", StationType.WEAPONS, false)

func _connect_station_buttons(station_name: String, station_type: StationType, is_player: bool) -> void:
	var base_node_name = "ScrollContainer_VBoxContainer_"
	var human_button_name = ""
	var easy_button_name = ""
	var normal_button_name = ""
	var hard_button_name = ""

	if is_player:
		base_node_name += "PlayerSubPanel_PlayerStations_" + station_name + "Station_" + station_name + "Options#"
		human_button_name = base_node_name + station_name + "Human"
		easy_button_name = base_node_name + station_name + "Easy"
		normal_button_name = base_node_name + station_name + "Normal"
		hard_button_name = base_node_name + station_name + "Hard"
	else:
		base_node_name += "EnemySubPanel_EnemyStations_Enemy" + station_name + "Station_Enemy" + station_name + "Options#"
		easy_button_name = base_node_name + "Enemy" + station_name + "Easy"
		normal_button_name = base_node_name + "Enemy" + station_name + "Normal"
		hard_button_name = base_node_name + "Enemy" + station_name + "Hard"

	# Connect Human button (only for player submarine)
	if is_player:
		var human_button = get_node(human_button_name)
		if human_button:
			human_button.connect("pressed", _on_assignment_selected.bind(station_type, AssignmentType.HUMAN, is_player))
		else:
			print("Error: Node not found: " + human_button_name)

	# Connect AI difficulty buttons
	var easy_button = get_node(easy_button_name)
	var normal_button = get_node(normal_button_name)
	var hard_button = get_node(hard_button_name)

	if easy_button:
		easy_button.connect("pressed", _on_assignment_selected.bind(station_type, AssignmentType.EASY_AI, is_player))
	else:
		print("Error: Node not found: " + easy_button_name)

	if normal_button:
		normal_button.connect("pressed", _on_assignment_selected.bind(station_type, AssignmentType.NORMAL_AI, is_player))
	else:
		print("Error: Node not found: " + normal_button_name)

	if hard_button:
		hard_button.connect("pressed", _on_assignment_selected.bind(station_type, AssignmentType.HARD_AI, is_player))
	else:
		print("Error: Node not found: " + hard_button_name)

func _on_assignment_selected(station_type: StationType, assignment_type: AssignmentType, is_player: bool) -> void:
	if is_player:
		# Handle human assignment logic
		if assignment_type == AssignmentType.HUMAN:
			# Clear previous human assignment
			if human_station_assigned:
				player_assignments[human_controlled_station] = AssignmentType.EASY_AI
			
			# Set new human assignment
			human_controlled_station = station_type
			human_station_assigned = true
		
		player_assignments[station_type] = assignment_type
	else:
		enemy_assignments[station_type] = assignment_type
	
	_update_button_states()
	_update_ui()

func _update_button_states() -> void:
	# Update player submarine button states
	_update_station_button_states("Radio", StationType.RADIO, true)
	_update_station_button_states("Captain", StationType.CAPTAIN, true)
	_update_station_button_states("Engineer", StationType.ENGINEER, true)
	_update_station_button_states("Weapons", StationType.WEAPONS, true)
	
	# Update enemy submarine button states
	_update_station_button_states("Radio", StationType.RADIO, false)
	_update_station_button_states("Captain", StationType.CAPTAIN, false)
	_update_station_button_states("Engineer", StationType.ENGINEER, false)
	_update_station_button_states("Weapons", StationType.WEAPONS, false)

func _update_station_button_states(station_name: String, station_type: StationType, is_player: bool) -> void:
	var full_node_name_base = "ScrollContainer_VBoxContainer_"
	var station_prefix = ""
	var button_suffix = ""

	if is_player:
		station_prefix = "PlayerSubPanel_PlayerStations_" + station_name + "Station_" + station_name + "Options#"
		button_suffix = station_name
	else:
		station_prefix = "EnemySubPanel_EnemyStations_Enemy" + station_name + "Station_Enemy" + station_name + "Options#"
		button_suffix = "Enemy" + station_name

	var assignment = player_assignments[station_type] if is_player else enemy_assignments[station_type]

	# Reset all buttons
	if is_player:
		var human_button = get_node(full_node_name_base + station_prefix + button_suffix + "Human")
		if human_button:
			human_button.button_pressed = false
		else:
			print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Human")

	var easy_button = get_node(full_node_name_base + station_prefix + button_suffix + "Easy")
	if easy_button:
		easy_button.button_pressed = false
	else:
		print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Easy")

	var normal_button = get_node(full_node_name_base + station_prefix + button_suffix + "Normal")
	if normal_button:
		normal_button.button_pressed = false
	else:
		print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Normal")

	var hard_button = get_node(full_node_name_base + station_prefix + button_suffix + "Hard")
	if hard_button:
		hard_button.button_pressed = false
	else:
		print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Hard")

	# Set the correct button as pressed
	match assignment:
		AssignmentType.HUMAN:
			if is_player:
				var human_button_to_press = get_node(full_node_name_base + station_prefix + button_suffix + "Human")
				if human_button_to_press:
					human_button_to_press.button_pressed = true
				else:
					print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Human")
		AssignmentType.EASY_AI:
			var easy_button_to_press = get_node(full_node_name_base + station_prefix + button_suffix + "Easy")
			if easy_button_to_press:
				easy_button_to_press.button_pressed = true
			else:
				print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Easy")
		AssignmentType.NORMAL_AI:
			var normal_button_to_press = get_node(full_node_name_base + station_prefix + button_suffix + "Normal")
			if normal_button_to_press:
				normal_button_to_press.button_pressed = true
			else:
				print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Normal")
		AssignmentType.HARD_AI:
			var hard_button_to_press = get_node(full_node_name_base + station_prefix + button_suffix + "Hard")
			if hard_button_to_press:
				hard_button_to_press.button_pressed = true
			else:
				print("Error: Node not found: " + full_node_name_base + station_prefix + button_suffix + "Hard")

func _update_ui() -> void:
	# Update status label
	var status_text = ""
	if human_station_assigned:
		var station_name = _get_station_name(human_controlled_station)
		status_text = "You will control: %s Station" % station_name
		start_game_button.disabled = false
	else:
		status_text = "Select at least one Human station to continue"
		start_game_button.disabled = true
	
	status_label.text = status_text

func _get_station_name(station_type: StationType) -> String:
	match station_type:
		StationType.RADIO:
			return "Radio Operator"
		StationType.CAPTAIN:
			return "Captain"
		StationType.ENGINEER:
			return "Engineer"
		StationType.WEAPONS:
			return "First Mate (Weapons)"
		_:
			return "Unknown"

func _on_start_game_button_pressed() -> void:
	if not human_station_assigned:
		return
	
	# Store assignments in GameState
	if GameState:
		GameState.player_assignments = player_assignments
		GameState.enemy_assignments = enemy_assignments
		GameState.human_controlled_station = human_controlled_station
	
	# Navigate to the appropriate station scene
	match human_controlled_station:
		StationType.RADIO:
			get_tree().change_scene_to_file("res://radio_station.tscn")
		StationType.CAPTAIN:
			get_tree().change_scene_to_file("res://game.tscn")
		StationType.ENGINEER:
			get_tree().change_scene_to_file("res://engineer_station.tscn")
		StationType.WEAPONS:
			get_tree().change_scene_to_file("res://weapons_station.tscn")

func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://main_menu.tscn")
