# Station-Based Crew Assignment System

## Overview
The new player selection system allows comprehensive crew assignment for both submarines. Players can assign Human control or AI difficulty levels to each of the four stations on both the player and enemy submarines.

## Key Features

### 1. Station-Based Selection
Each submarine has four distinct stations:
- **Radio Operator**: Enemy tracking and communication
- **Captain**: Navigation and tactical command
- **Engineer**: System maintenance and repairs
- **First Mate (Weapons)**: Combat systems and weapons

### 2. Assignment Options

#### For Player Submarine (Alpha Team):
- **Human**: Player controls this station directly
- **Easy AI**: Forgiving AI assistance
- **Normal AI**: Balanced AI performance
- **Hard AI**: Expert AI assistance

#### For Enemy Submarine (Bravo Team):
- **Easy AI**: Less challenging opponent
- **Normal AI**: Balanced challenge level
- **Hard AI**: Expert AI opponent

### 3. Human Player Control
- **Single Station Control**: Player can only control one station at a time
- **Station Selection**: Choose which station to personally manage
- **Tooltip Guidance**: Clear explanation of Human option effects
- **Automatic Navigation**: Game launches directly to selected station

## User Interface

### Layout Structure
```
Captain Sonar - Crew Assignment
├── Your Submarine (Alpha Team)
│   ├── Radio Operator: [Human] [Easy AI] [Normal AI] [Hard AI]
│   ├── Captain: [Human] [Easy AI] [Normal AI] [Hard AI]
│   ├── Engineer: [Human] [Easy AI] [Normal AI] [Hard AI]
│   └── First Mate: [Human] [Easy AI] [Normal AI] [Hard AI]
├── Enemy Submarine (Bravo Team)
│   ├── Radio Operator: [Easy AI] [Normal AI] [Hard AI]
│   ├── Captain: [Easy AI] [Normal AI] [Hard AI]
│   ├── Engineer: [Easy AI] [Normal AI] [Hard AI]
│   └── First Mate: [Easy AI] [Normal AI] [Hard AI]
└── [Start Game] Button
```

### Visual Features
- **Scrollable Interface**: Accommodates all options without crowding
- **Clear Separation**: Distinct sections for each submarine
- **Proper Spacing**: 10px separation between station rows
- **Toggle Buttons**: Visual feedback for current selections
- **Status Display**: Shows which station player will control
- **Tooltips**: Helpful explanations for Human option

## Assignment Logic

### Human Station Assignment
```gdscript
func _on_assignment_selected(station_type: StationType, assignment_type: AssignmentType, is_player: bool):
    if assignment_type == AssignmentType.HUMAN:
        # Clear previous human assignment
        if human_station_assigned:
            player_assignments[human_controlled_station] = AssignmentType.NORMAL_AI
        
        # Set new human assignment
        human_controlled_station = station_type
        human_station_assigned = true
```

### Validation Rules
1. **Single Human Control**: Only one station can be Human-controlled
2. **Required Selection**: At least one Human station must be selected
3. **Automatic Defaults**: All stations default to Normal AI
4. **Clear Previous**: Selecting new Human station clears previous assignment

## Technical Implementation

### Data Storage
```gdscript
# Store assignments for each station on each submarine
var player_assignments: Dictionary = {}  # StationType -> AssignmentType
var enemy_assignments: Dictionary = {}   # StationType -> AssignmentType
var human_controlled_station: StationType
var human_station_assigned: bool
```

### Button Management
- **Dynamic Connections**: Buttons connected based on station and submarine
- **State Synchronization**: Button states reflect current assignments
- **Toggle Groups**: Only one option per station can be selected
- **Visual Feedback**: Pressed state shows current selection

### Scene Navigation
Based on human-controlled station:
- **Radio Operator** → `radio_station.tscn`
- **Captain** → `game.tscn`
- **Engineer** → `engineer_station.tscn`
- **First Mate** → `weapons_station.tscn`

## User Experience

### Workflow
1. **Open Selection**: Navigate from main menu to crew assignment
2. **Review Options**: See all stations for both submarines
3. **Assign Crew**: Select Human or AI for each station
4. **Choose Control**: Pick which station to personally control
5. **Start Game**: Launch directly to selected station interface

### Feedback Systems
- **Status Updates**: Real-time display of current selections
- **Button States**: Visual indication of active choices
- **Validation Messages**: Clear guidance on requirements
- **Tooltips**: Helpful explanations for each option

### Default Behavior
- **All Normal AI**: Every station starts with Normal AI difficulty
- **No Human**: Player must explicitly choose a station to control
- **Disabled Start**: Start button disabled until Human station selected
- **Clear Instructions**: Status label guides user through process

## AI Difficulty Levels

### Easy AI
- **Forgiving Gameplay**: Makes suboptimal decisions
- **Slower Reactions**: Delayed response times
- **Simple Strategies**: Basic tactical approaches
- **Learning Friendly**: Good for new players

### Normal AI
- **Balanced Performance**: Reasonable decision making
- **Standard Timing**: Normal reaction speeds
- **Moderate Strategy**: Competent tactical play
- **Default Choice**: Recommended for most players

### Hard AI
- **Expert Performance**: Optimal decision making
- **Quick Reactions**: Fast response times
- **Advanced Strategy**: Sophisticated tactical approaches
- **Challenge Mode**: For experienced players

## Benefits

### For Players
- **Full Control**: Choose exactly how to play
- **Flexible Difficulty**: Set different challenges per station
- **Clear Interface**: Easy to understand and navigate
- **Immediate Feedback**: See selections in real-time

### For Gameplay
- **Balanced Teams**: Customize both submarine crews
- **Scalable Challenge**: Adjust difficulty per station
- **Role Focus**: Concentrate on preferred station type
- **Strategic Depth**: Different AI levels create varied gameplay

## Future Enhancements
- **Save Presets**: Store favorite crew configurations
- **Multiplayer Support**: Assign human players to multiple stations
- **AI Personalities**: Different AI behavior styles per difficulty
- **Performance Metrics**: Track AI effectiveness per station
- **Custom Difficulty**: Fine-tune AI parameters per station
