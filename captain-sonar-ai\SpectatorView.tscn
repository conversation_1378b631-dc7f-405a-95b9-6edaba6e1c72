[gd_scene load_steps=4 format=3 uid="uid://1qs7nqcie3v2"]

[ext_resource type="Script" uid="uid://67krs76epl4g" path="res://SpectatorView.gd" id="1"]
[ext_resource type="Texture2D" uid="uid://dmfkpv1eqvqyk" path="res://player_icon.svg" id="2"]
[ext_resource type="Texture2D" uid="uid://xj62mg58llei" path="res://enemy_sub_icon.svg" id="3"]

[node name="SpectatorView" type="Control"]
layout_mode = 3
anchors_preset = 0
script = ExtResource("1")

[node name="TileMap" type="TileMap" parent="."]
format = 2

[node name="TileMapLayer" type="TileMapLayer" parent="TileMap"]

[node name="AlphaSub" type="Sprite2D" parent="."]
texture = ExtResource("2")

[node name="BravoSub" type="Sprite2D" parent="."]
texture = ExtResource("3")
