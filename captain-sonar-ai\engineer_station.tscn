[gd_scene load_steps=2 format=3 uid="uid://b3ahbksuxcyba"]

[ext_resource type="Script" uid="uid://crsh0lc5sh34n" path="res://engineer_station.gd" id="1_engineer"]

[node name="EngineerStation" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_engineer")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="StatusLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 50.0
offset_top = 80.0
offset_right = 500.0
offset_bottom = 400.0
text = "Engineer Station - Loading..."

[node name="SystemsPanel" type="VBoxContainer" parent="."]
layout_mode = 0
offset_left = 600.0
offset_top = 100.0
offset_right = 800.0
offset_bottom = 400.0

[node name="SystemsLabel" type="Label" parent="SystemsPanel"]
layout_mode = 2
text = "System Repairs"
horizontal_alignment = 1

[node name="RepairWeaponsButton" type="Button" parent="SystemsPanel"]
layout_mode = 2
text = "Repair Weapons"

[node name="RepairSonarButton" type="Button" parent="SystemsPanel"]
layout_mode = 2
text = "Repair Sonar"

[node name="RepairEngineButton" type="Button" parent="SystemsPanel"]
layout_mode = 2
text = "Repair Engine"

[node name="RepairSpecialButton" type="Button" parent="SystemsPanel"]
layout_mode = 2
text = "Repair Special"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="SystemsPanel/RepairWeaponsButton" to="." method="_repair_weapons"]
[connection signal="pressed" from="SystemsPanel/RepairSonarButton" to="." method="_repair_sonar"]
[connection signal="pressed" from="SystemsPanel/RepairEngineButton" to="." method="_repair_engine"]
[connection signal="pressed" from="SystemsPanel/RepairSpecialButton" to="." method="_repair_special"]
