# Captain Station Interface

## Overview
The Captain Station is the main game interface where the captain controls their submarine and views the tactical situation on the game board.

## Features

### Game Board
- **15x15 grid map** with water tiles and island borders
- **Player submarine** (blue) - always visible, shows your current position and heading
- **Enemy submarine** (red) - only visible when the enemy has surfaced
- **Real-time position updates** based on movement commands

### Movement Controls
Located on the right side of the screen:
- **North** - Move submarine north (up)
- **South** - Move submarine south (down) 
- **East** - Move submarine east (right)
- **West** - Move submarine west (left)

### Status Display
Shows current submarine information:
- Position coordinates (x, y)
- Current heading direction
- Health points
- Heat level

### Test Controls
For demonstration purposes:
- **Surface Enemy** - Makes the enemy submarine visible on the map
- **Submerge Enemy** - Hides the enemy submarine from view

## Game Mechanics

### Submarine Visibility
- **Your submarine**: Always visible to you as the captain
- **Enemy submarine**: Only visible when they surface (is_surfaced = true)
- This simulates the real Captain Sonar game where you can only see enemy submarines when they surface

### Movement System
- Each movement command updates the submarine's position on the grid
- Movement is restricted by map boundaries (islands around the edges)
- Each move increases heat and is logged for tracking

### Integration with Game State
- Uses the GameState autoload system for centralized state management
- Connects to state_updated signals for real-time updates
- Supports multiple teams (alpha/bravo) with proper team identification

## Technical Implementation

### Files Modified/Created
1. **game.gd** - Main Captain station logic
2. **game.tscn** - UI layout with map, controls, and submarine sprites
3. **GameState.gd** - Enhanced with proper initialization
4. **project.godot** - Added GameState as autoload

### Key Components
- TileMap system for the game board
- Sprite2D nodes for submarine representation
- VBoxContainer layout for movement controls
- Signal-based communication between components

## Usage
1. Launch the game and navigate to the Captain Station
2. Use movement buttons to control your submarine
3. Watch the status display for current submarine information
4. Use test controls to simulate enemy submarine surfacing
5. The enemy submarine will only appear when surfaced

## Future Enhancements
- Add collision detection with islands
- Implement weapon systems integration
- Add sonar ping visualization
- Connect to multiplayer networking
- Add sound effects and animations
