# Submarine Movement System

## Overview
The submarine movement system has been completely overhauled to provide precise, grid-based movement with proper boundary checking and collision detection.

## Key Improvements

### 1. Grid-Based Movement
- **One Space Per Move**: Submarines now move exactly one grid space per command
- **Larger Tiles**: Increased tile size from 64px to 96px for better submarine visibility
- **Precise Positioning**: Submarines are perfectly centered on grid tiles
- **Proper Scaling**: Submarine sprites scaled to 0.7x to fit nicely within tiles

### 2. Boundary Checking
- **Map Boundaries**: Prevents movement outside the 15x15 grid (0,0 to 14,14)
- **Island Collision**: Submarines cannot move into island tiles (border areas)
- **Safe Starting Positions**: Submarines start at (2,2) and (12,12) in safe water areas

### 3. Movement Validation
```gdscript
# Example of boundary and collision checking
if new_position.x < 0 or new_position.x >= 15 or new_position.y < 0 or new_position.y >= 15:
    print("Cannot move - would go outside map boundaries!")
    return

if map_data[new_position.x][new_position.y] == true:  # Island collision
    print("Cannot move - would hit an island!")
    return
```

### 4. Visual Feedback
- **Movement Blocking**: Console messages when moves are blocked
- **Position Display**: Real-time position coordinates in status bar
- **Heading Indicators**: Submarine sprites rotate to show current heading

## Technical Details

### Map Layout
- **15x15 Grid**: Total map size with coordinates (0,0) to (14,14)
- **Border Islands**: Outer edge tiles (x=0, x=14, y=0, y=14) are islands
- **Water Area**: Inner 13x13 area (1,1) to (13,13) is navigable water
- **Starting Positions**: 
  - Alpha submarine: (2,2) facing East
  - Bravo submarine: (12,12) facing West

### Movement Constraints
1. **Boundary Check**: Position must be within (0,0) to (14,14)
2. **Island Check**: Target tile must be water (false in map_data)
3. **One Tile**: Each move command moves exactly one grid space
4. **Direction Lock**: Submarine heading updates with each move

### Positioning System
```gdscript
# Convert grid coordinates to world position
var tilemap_offset = Vector2(50, 80)  # TileMap position
var world_pos = Vector2(grid_position) * TILE_SIZE + tilemap_offset + Vector2(TILE_SIZE/2, TILE_SIZE/2)
```

## Movement Commands

### Available Directions
- **NORTH**: Move up (y-1)
- **SOUTH**: Move down (y+1) 
- **EAST**: Move right (x+1)
- **WEST**: Move left (x-1)

### Movement Process
1. Player clicks movement button
2. System calculates new position
3. Validates boundaries and collisions
4. Updates submarine position if valid
5. Updates heading and game state
6. Provides visual feedback

## Error Handling

### Blocked Movement Messages
- "Cannot move NORTH - would go outside map boundaries!"
- "Cannot move EAST - would hit an island!"
- "Movement blocked: Cannot move SOUTH"

### Console Output
All movement validation results are logged to console for debugging and player feedback.

## Visual Improvements

### Submarine Display
- **Larger Sprites**: 0.7x scale (up from 0.5x) for better visibility
- **Centered Positioning**: Perfectly aligned with grid tiles
- **Rotation**: Sprites rotate to show current heading direction
- **Visibility Rules**: Enemy submarine only visible when surfaced

### Grid Display
- **96px Tiles**: Larger tiles accommodate submarine sprites better
- **Clear Boundaries**: Distinct island borders around map edges
- **Water Areas**: Blue tiles for navigable water
- **Island Areas**: Different colored tiles for obstacles

## Usage

### For Players
1. Use directional buttons to move submarine
2. Watch status display for current position
3. Observe submarine rotation for heading
4. Note when moves are blocked by boundaries or islands

### For Developers
1. Movement validation in `GameState.process_captain_move()`
2. Position updates in `game.gd._update_submarine_positions()`
3. Visual feedback in movement command functions
4. Map data stored in `GameState.map_data` for collision detection

## Future Enhancements
- Add sound effects for movement and blocked moves
- Visual indicators for valid/invalid move targets
- Animation for submarine movement between tiles
- Damage system for hitting islands
- Advanced pathfinding for AI submarines
