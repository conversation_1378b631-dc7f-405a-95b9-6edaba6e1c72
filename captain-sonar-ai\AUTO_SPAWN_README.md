# Automatic Submarine Spawning System

## Overview
The submarine spawning system has been completely redesigned to automatically place submarines in valid water areas, ensuring they never spawn on islands or in invalid positions.

## Key Features

### 1. Intelligent Spawn Detection
- **Scans entire map** for valid water tiles (non-island positions)
- **Avoids edge areas** by maintaining a 2-tile buffer from map boundaries
- **Finds optimal separation** between submarines for strategic gameplay

### 2. Dynamic Map Support
- **Flexible map sizes** - works with any map dimensions
- **Reads actual map data** to determine valid positions
- **Adapts to custom map layouts** automatically

### 3. Positioning Algorithm
```gdscript
func _find_valid_spawn_positions() -> Array[Vector2i]:
    # Scan for water tiles with safety buffer
    for x in range(2, map_width - 2):
        for y in range(2, map_height - 2):
            if not map_data[x][y]:  # Water tile found
                valid_positions.append(Vector2i(x, y))
    
    # Select positions with maximum separation
    return [closest_to_center, farthest_from_center]
```

### 4. Submarine Alignment
- **Precise positioning** accounting for TileMap scale and offset
- **Centered placement** within grid tiles
- **Consistent scaling** for both submarines (0.2x scale)

## Technical Implementation

### Map Analysis
The system analyzes the map structure to:
1. **Identify water tiles** (false values in map_data)
2. **Avoid island tiles** (true values in map_data)
3. **Maintain safe distances** from map edges
4. **Calculate optimal separation** between spawn points

### Position Calculation
```gdscript
# Account for TileMap transformation
var tilemap_offset = Vector2(183, 60)
var tilemap_scale = Vector2(0.25, 0.25)
var scaled_tile_size = TILE_SIZE * tilemap_scale.x
var world_pos = Vector2(grid_position) * scaled_tile_size + tilemap_offset + Vector2(scaled_tile_size/2, scaled_tile_size/2)
```

### Boundary Validation
- **Dynamic boundaries** based on actual map size
- **Island collision detection** using map data
- **Safe movement validation** for all directions

## Spawn Strategy

### Position Selection
1. **Scan Phase**: Find all valid water positions
2. **Filter Phase**: Remove positions too close to edges
3. **Selection Phase**: Choose positions with good separation
4. **Fallback Phase**: Use default positions if no valid spots found

### Separation Logic
- **Center-based sorting** to find spread-out positions
- **Maximum distance** between submarines for strategic gameplay
- **Balanced placement** ensuring fair starting positions

### Safety Measures
- **Edge buffer**: 2-tile minimum distance from map boundaries
- **Island avoidance**: Never spawn on or adjacent to islands
- **Fallback positions**: Default to (2,2) and (12,12) if scanning fails

## Visual Alignment

### TileMap Integration
- **Scale compensation**: Accounts for 0.25x TileMap scale
- **Offset adjustment**: Handles TileMap position (183, 60)
- **Precise centering**: Submarines perfectly aligned with grid

### Submarine Scaling
- **Consistent size**: Both submarines use 0.2x scale
- **Proper fit**: Sized to fit within scaled grid tiles
- **Visual clarity**: Clear distinction between submarines and map

## Status Display

### Enhanced Information
- **Grid coordinates**: Shows exact (x, y) position
- **Enemy status**: Indicates if enemy is surfaced or submerged
- **Movement feedback**: Real-time position updates
- **Heading display**: Current submarine direction

## Error Handling

### Fallback Systems
1. **No valid positions**: Uses default safe coordinates
2. **Map data missing**: Falls back to standard 15x15 assumptions
3. **Single position**: Places both submarines at same safe spot
4. **Invalid movement**: Prevents moves that would cause collisions

### Debug Information
- **Console logging**: Reports spawn position selection
- **Movement validation**: Logs blocked moves with reasons
- **Map analysis**: Shows valid position count and selection

## Benefits

### For Players
- **Always valid spawns**: Never start in impossible positions
- **Fair placement**: Balanced starting positions for both teams
- **Clear visibility**: Submarines properly aligned with map grid
- **Predictable behavior**: Consistent spawning across different maps

### For Developers
- **Flexible system**: Works with any map size or layout
- **Easy maintenance**: Automatic adaptation to map changes
- **Robust validation**: Comprehensive error checking and fallbacks
- **Debug friendly**: Extensive logging for troubleshooting

## Future Enhancements
- **Custom spawn zones**: Define specific areas for submarine placement
- **Team-based spawning**: Different spawn rules for different teams
- **Spawn animations**: Visual effects when submarines appear
- **Spawn preferences**: Player-selectable starting positions
- **Advanced separation**: Consider strategic value of spawn positions
