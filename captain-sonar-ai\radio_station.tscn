[gd_scene load_steps=10 format=3 uid="uid://bxe2noq6pdmqm"]

[ext_resource type="Script" uid="uid://d01l26aki7uqg" path="res://radio_station.gd" id="1_3kiii"]
[ext_resource type="Texture2D" uid="uid://bb0sw70pr4jjx" path="res://water_icon.svg" id="1_1855q"]
[ext_resource type="Texture2D" uid="uid://c6keek6x4a7lg" path="res://island_icon.svg" id="2_sg0re"]
[ext_resource type="Script" uid="uid://cn44kio7h01mh" path="res://DraggableOverlay.gd" id="3_vdbti"]
[ext_resource type="PackedScene" uid="uid://do22gtwwnkviq" path="res://PathOverlay.tscn" id="4_b0s5h"]
[ext_resource type="Script" uid="uid://bsd6vc6ytssvq" path="res://MoveEntry.gd" id="4_sg0re"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_3kiii"]
texture = ExtResource("1_1855q")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
0:6/0 = 0
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
7:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_b0s5h"]
texture = ExtResource("2_sg0re")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
0:6/0 = 0
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
7:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0

[sub_resource type="TileSet" id="TileSet_3kiii"]
custom_data_layer_0/name = "is_land"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_3kiii")
sources/3 = SubResource("TileSetAtlasSource_b0s5h")

[node name="RadioStation" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_3kiii")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="MapPanel" type="PanelContainer" parent="HBoxContainer"]
layout_mode = 2

[node name="SubViewportContainer" type="SubViewportContainer" parent="HBoxContainer/MapPanel"]
layout_mode = 2

[node name="TileMap" type="TileMap" parent="HBoxContainer/MapPanel/SubViewportContainer"]
format = 2

[node name="TileMapLayer" type="TileMapLayer" parent="HBoxContainer/MapPanel/SubViewportContainer/TileMap"]
position = Vector2(550, 300)
tile_set = SubResource("TileSet_3kiii")

[node name="PathOverlay" parent="HBoxContainer/MapPanel/SubViewportContainer/TileMap" instance=ExtResource("4_b0s5h")]

[node name="Control" type="Control" parent="HBoxContainer/MapPanel/SubViewportContainer"]
layout_mode = 2
script = ExtResource("3_vdbti")

[node name="Line2D" type="Line2D" parent="HBoxContainer/MapPanel/SubViewportContainer/Control"]

[node name="MovesPanel" type="PanelContainer" parent="HBoxContainer"]
layout_mode = 2
script = ExtResource("4_sg0re")

[node name="ScrollContainer" type="ScrollContainer" parent="HBoxContainer/MovesPanel"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="HBoxContainer/MovesPanel/ScrollContainer"]
layout_mode = 2

[node name="MoveEntry" type="Control" parent="HBoxContainer/MovesPanel/ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="CommPanel" type="PanelContainer" parent="HBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="HBoxContainer/CommPanel"]
layout_mode = 2

[node name="Label2" type="Label" parent="HBoxContainer/CommPanel"]
layout_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="HBoxContainer/CommPanel"]
layout_mode = 2

[node name="LineEdit" type="LineEdit" parent="HBoxContainer/CommPanel/HBoxContainer"]
layout_mode = 2

[node name="Button" type="Button" parent="HBoxContainer/CommPanel/HBoxContainer"]
layout_mode = 2

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
