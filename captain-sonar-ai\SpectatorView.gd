extends Control

@onready var tile_map = $TileMap
@onready var alpha_sprite = $AlphaSub
@onready var bravo_sprite = $BravoSub

const TILE_SIZE = 64

func _ready() -> void:
	if Engine.has_singleton("GameState"):
		var state = Engine.get_singleton("GameState")
	GameState.connect("state_updated", _on_state_updated)
	_update_sub_positions()

func _on_state_updated(team_id: String) -> void:
	_update_sub_positions()

func _update_sub_positions() -> void:
	for team_id in GameState.TEAM_IDS:
		var sub: Sprite2D = team_id == "alpha" ? alpha_sprite : bravo_sprite
		var s: SubmarineState = GameState.teams[team_id]
		sub.position = Vector2(s.position) * TILE_SIZE
		sub.rotation_degrees = _dir_to_rotation(s.heading)

func _dir_to_rotation(dir: String) -> float:
	match dir:
		"NORTH":
			return -90
		"SOUTH":
			return 90
		"EAST":
			return 0
		"WEST":
			return 180
		_:
			return 0
