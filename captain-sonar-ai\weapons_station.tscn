[gd_scene load_steps=2 format=3 uid="uid://cy2fmbyuqrwyt"]

[ext_resource type="Script" uid="uid://bga72hj7hkwqg" path="res://weapons_station.gd" id="1_weapons"]

[node name="WeaponsStation" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_weapons")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="StatusLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 50.0
offset_top = 80.0
offset_right = 500.0
offset_bottom = 400.0
text = "Weapons Station - Loading..."

[node name="WeaponsPanel" type="VBoxContainer" parent="."]
layout_mode = 0
offset_left = 600.0
offset_top = 100.0
offset_right = 800.0
offset_bottom = 400.0

[node name="WeaponsLabel" type="Label" parent="WeaponsPanel"]
layout_mode = 2
text = "Weapons Control"
horizontal_alignment = 1

[node name="TorpedoButton" type="Button" parent="WeaponsPanel"]
layout_mode = 2
text = "Fire Torpedo"

[node name="MineButton" type="Button" parent="WeaponsPanel"]
layout_mode = 2
text = "Deploy Mine"

[node name="DroneButton" type="Button" parent="WeaponsPanel"]
layout_mode = 2
text = "Launch Drone"

[node name="SonarPingButton" type="Button" parent="WeaponsPanel"]
layout_mode = 2
text = "Sonar Ping"

[node name="ChargeWeaponsButton" type="Button" parent="WeaponsPanel"]
layout_mode = 2
text = "Charge Weapons"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="WeaponsPanel/TorpedoButton" to="." method="_fire_torpedo"]
[connection signal="pressed" from="WeaponsPanel/MineButton" to="." method="_deploy_mine"]
[connection signal="pressed" from="WeaponsPanel/DroneButton" to="." method="_launch_drone"]
[connection signal="pressed" from="WeaponsPanel/SonarPingButton" to="." method="_sonar_ping"]
[connection signal="pressed" from="WeaponsPanel/ChargeWeaponsButton" to="." method="_charge_weapons"]
