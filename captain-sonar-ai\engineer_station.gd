extends Control

# Engineer Station - Manages submarine systems and repairs
# The engineer is responsible for maintaining submarine systems and managing power

@onready var back_button = $BackButton
@onready var status_label = $StatusLabel
@onready var systems_panel = $SystemsPanel

var player_team_id = "alpha"

func _ready() -> void:
	_setup_ui()
	
	# Connect to GameState if available
	if GameState:
		if not GameState.is_connected("state_updated", _on_state_updated):
			GameState.connect("state_updated", _on_state_updated)
		_update_display()

func _setup_ui() -> void:
	# Setup the engineer interface
	if status_label:
		status_label.text = "Engineer Station - System Status"

func _on_state_updated(team_id: String) -> void:
	if team_id == player_team_id:
		_update_display()

func _update_display() -> void:
	if not GameState or not GameState.teams.has(player_team_id):
		return
		
	var player_state: SubmarineState = GameState.teams[player_team_id]
	
	# Update status display with system information
	var status_text = "Engineer Station\n"
	status_text += "Heat Level: %.1f\n" % player_state.heat
	status_text += "Health: %d\n" % player_state.health
	status_text += "\nSystem Status:\n"
	
	for system_name in player_state.systems:
		var system_state = player_state.systems[system_name]
		var state_text = ""
		match system_state:
			SubmarineState.SystemState.OPERATIONAL:
				state_text = "OPERATIONAL"
			SubmarineState.SystemState.DAMAGED:
				state_text = "DAMAGED"
			SubmarineState.SystemState.OFFLINE:
				state_text = "OFFLINE"
		
		status_text += "- %s: %s\n" % [system_name.capitalize(), state_text]
	
	if status_label:
		status_label.text = status_text

func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://player_selection.tscn")

# System repair functions (placeholder for future implementation)
func _repair_weapons() -> void:
	_repair_system("weapons")

func _repair_sonar() -> void:
	_repair_system("sonar")

func _repair_engine() -> void:
	_repair_system("engine")

func _repair_special() -> void:
	_repair_system("special")

func _repair_system(system_name: String) -> void:
	if not GameState or not GameState.teams.has(player_team_id):
		return
		
	var player_state: SubmarineState = GameState.teams[player_team_id]
	if player_state.systems.has(system_name):
		# Simple repair logic - restore to operational if damaged
		if player_state.systems[system_name] == SubmarineState.SystemState.DAMAGED:
			player_state.systems[system_name] = SubmarineState.SystemState.OPERATIONAL
			GameState.emit_signal("state_updated", player_team_id)
			print("Repaired %s system" % system_name)
