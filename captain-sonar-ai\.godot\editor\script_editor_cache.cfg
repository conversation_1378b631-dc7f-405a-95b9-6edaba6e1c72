[res://MainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 16,
"scroll_position": 0.0,
"selection": true,
"selection_from_column": 40,
"selection_from_line": 16,
"selection_to_column": 56,
"selection_to_line": 16,
"syntax_highlighter": "GDScript"
}

[res://game.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 35,
"scroll_position": 21.0,
"selection": true,
"selection_from_column": 0,
"selection_from_line": 35,
"selection_to_column": 63,
"selection_to_line": 36,
"syntax_highlighter": "GDScript"
}

[res://settings.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://help.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIProfile.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 48,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 10,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://DraggableOverlay.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 3,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://MoveEntry.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://path_overlay.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 20,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://radio_station.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 16,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://SpectatorView.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 35,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 19,
"scroll_position": 4.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://GameState.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 72,
"scroll_position": 67.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://PlayerSelection.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 66,
"scroll_position": 59.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://PLAYER_SELECTION_README.md]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "Markdown"
}

[res://SubmarineState.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
