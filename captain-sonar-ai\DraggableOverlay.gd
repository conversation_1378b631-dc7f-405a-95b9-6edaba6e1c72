extends Control
var tile_size = Vector2(64,64)
var dragging = false
var drag_offset = Vector2()

func _gui_input(ev):
	if ev is InputEventMouseButton and ev.button_index==MOUSE_BUTTON_LEFT:
		if ev.pressed:
			start_drag(ev.position)
		else:
			stop_drag()

func start_drag(mouse_position: Vector2) -> void:
	dragging = true
	drag_offset = get_global_mouse_position() - global_position

func stop_drag() -> void:
	dragging = false
func _process(delta):
	if dragging:
		global_position = (get_global_mouse_position() / tile_size).floor() * tile_size
