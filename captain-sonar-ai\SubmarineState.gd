extends Resource
class_name SubmarineState

# Represents all mutable data for a single submarine.

# Enumeration for system health states.
enum SystemState { OPERATIONAL, DAMAGED, OFFLINE }

@export var position: Vector2i = Vector2i.ZERO
@export var heading: String = "NORTH"
@export var health: int = 4
@export var heat: float = 0.0
@export var systems := {
	"weapons": SystemState.OPERATIONAL,
	"sonar": SystemState.OPERATIONAL,
	"engine": SystemState.OPERATIONAL,
	"special": SystemState.OPERATIONAL,
}
@export var weapons_charge := {
	"torpedo": 0.0,
	"mine": 0.0,
	"drone": 0.0,
	"sonar_ping": 0.0,
}
var move_log: Array[String] = []
var is_surfaced: bool = false
var radio_frequency: float = 0.0
