[editor_metadata]

executable_path="D:/Godot/Godot_v4.4.1-stable_win64.exe"
use_advanced_connections=false

[recent_files]

scenes=["res://player_selection.tscn", "res://game.tscn", "res://radio_station.tscn", "res://weapons_station.tscn", "res://engineer_station.tscn", "res://SpectatorView.tscn", "res://PathOverlay.tscn", "res://bridge.tscn", "res://EnemyAI.tscn", "res://player.tscn"]
scripts=["res://SubmarineState.gd", "res://PLAYER_SELECTION_README.md", "res://PlayerSelection.gd", "VBoxContainer", "res://GameState.gd", "res://SpectatorView.gd", "res://radio_station.gd", "res://path_overlay.gd", "res://MoveEntry.gd", "res://DraggableOverlay.gd"]

[dialog_bounds]

create_new_node=Rect2(510, 190, 900, 700)

[script_setup]

last_selected_language="GDScript"

[game_view]

embed_size_mode=0

[color_picker]

picker_shape=3
recent_presets=PackedColorArray(1, 1, 1, 1)

[quick_open_dialog]

last_mode=1
