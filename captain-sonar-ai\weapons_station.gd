extends Control

# Weapons Station (First Mate) - Manages submarine weapons and combat systems
# The First Mate is responsible for weapons, mines, torpedoes, and sonar pings

@onready var back_button = $BackButton
@onready var status_label = $StatusLabel
@onready var weapons_panel = $WeaponsPanel

var player_team_id = "alpha"

func _ready() -> void:
	_setup_ui()
	
	# Connect to GameState if available
	if GameState:
		if not GameState.is_connected("state_updated", _on_state_updated):
			GameState.connect("state_updated", _on_state_updated)
		_update_display()

func _setup_ui() -> void:
	# Setup the weapons interface
	if status_label:
		status_label.text = "Weapons Station - Combat Systems"

func _on_state_updated(team_id: String) -> void:
	if team_id == player_team_id:
		_update_display()

func _update_display() -> void:
	if not GameState or not GameState.teams.has(player_team_id):
		return
		
	var player_state: SubmarineState = GameState.teams[player_team_id]
	
	# Update status display with weapons information
	var status_text = "Weapons Station (First Mate)\n"
	status_text += "Health: %d\n" % player_state.health
	status_text += "\nWeapons Status:\n"
	
	# Show weapons system status
	var weapons_system_state = player_state.systems.get("weapons", SubmarineState.SystemState.OPERATIONAL)
	var weapons_status = ""
	match weapons_system_state:
		SubmarineState.SystemState.OPERATIONAL:
			weapons_status = "OPERATIONAL"
		SubmarineState.SystemState.DAMAGED:
			weapons_status = "DAMAGED"
		SubmarineState.SystemState.OFFLINE:
			weapons_status = "OFFLINE"
	
	status_text += "Weapons System: %s\n\n" % weapons_status
	
	# Show weapon charge levels
	status_text += "Weapon Charges:\n"
	for weapon_name in player_state.weapons_charge:
		var charge = player_state.weapons_charge[weapon_name]
		status_text += "- %s: %.1f%%\n" % [weapon_name.capitalize(), charge * 100]
	
	if status_label:
		status_label.text = status_text

func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://player_selection.tscn")

# Weapon functions (placeholder for future implementation)
func _fire_torpedo() -> void:
	_use_weapon("torpedo")

func _deploy_mine() -> void:
	_use_weapon("mine")

func _launch_drone() -> void:
	_use_weapon("drone")

func _sonar_ping() -> void:
	_use_weapon("sonar_ping")

func _use_weapon(weapon_name: String) -> void:
	if not GameState or not GameState.teams.has(player_team_id):
		return
		
	var player_state: SubmarineState = GameState.teams[player_team_id]
	
	# Check if weapons system is operational
	if player_state.systems.get("weapons", SubmarineState.SystemState.OPERATIONAL) != SubmarineState.SystemState.OPERATIONAL:
		print("Weapons system is not operational!")
		return
	
	# Check if weapon is charged
	if player_state.weapons_charge.has(weapon_name):
		var charge = player_state.weapons_charge[weapon_name]
		if charge >= 1.0:
			# Use weapon and reset charge
			player_state.weapons_charge[weapon_name] = 0.0
			GameState.emit_signal("state_updated", player_team_id)
			print("Fired %s!" % weapon_name)
		else:
			print("%s is not fully charged (%.1f%%)" % [weapon_name, charge * 100])

# Charge weapons over time (placeholder)
func _charge_weapons() -> void:
	if not GameState or not GameState.teams.has(player_team_id):
		return
		
	var player_state: SubmarineState = GameState.teams[player_team_id]
	
	# Increase all weapon charges slightly
	for weapon_name in player_state.weapons_charge:
		if player_state.weapons_charge[weapon_name] < 1.0:
			player_state.weapons_charge[weapon_name] = min(1.0, player_state.weapons_charge[weapon_name] + 0.1)
	
	GameState.emit_signal("state_updated", player_team_id)
