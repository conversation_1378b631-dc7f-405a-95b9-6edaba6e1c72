extends Node

signal submarine_moved(team_id, direction)
signal state_updated(team_id)

const TEAM_IDS = ["alpha", "bravo"]

# Player role and AI difficulty selections
enum PlayerRole {
	NONE,
	RADIO_OPERATOR,
	CAPTAIN,
	ENGINEER,
	FIRST_MATE
}

enum AIDifficulty {
	NONE,
	EASY,
	MEDIUM,
	HARD
}

var teams: Dictionary = {}
var player_role: PlayerRole = PlayerRole.NONE
var ai_difficulty: AIDifficulty = AIDifficulty.NONE
var map_data: Array = []  # Store map data for collision detection

# New station-based assignment system
var player_assignments: Dictionary = {}
var enemy_assignments: Dictionary = {}
var human_controlled_station: int = 0  # StationType enum value

# Initializes a fresh match. This should be called at the start of a game.
func initialize_game(map_data_input: Array) -> void:
	teams.clear()
	map_data = map_data_input  # Store map data for collision detection

	for id in TEAM_IDS:
		teams[id] = SubmarineState.new()

	# Automatically find valid spawn positions for submarines
	var spawn_positions = _find_valid_spawn_positions()

	if teams.has("alpha") and spawn_positions.size() > 0:
		teams["alpha"].position = spawn_positions[0]
		teams["alpha"].heading = "EAST"

	if teams.has("bravo") and spawn_positions.size() > 1:
		teams["bravo"].position = spawn_positions[1]
		teams["bravo"].heading = "WEST"
		teams["bravo"].is_surfaced = false  # Enemy starts submerged

	# Emit initial state updates
	for team_id in TEAM_IDS:
		emit_signal("state_updated", team_id)

# Find valid water positions for submarine spawning
func _find_valid_spawn_positions() -> Array[Vector2i]:
	var valid_positions: Array[Vector2i] = []

	if map_data.size() == 0:
		# Fallback positions if no map data
		return [Vector2i(2, 2), Vector2i(12, 12)]

	# Scan the map for water tiles (avoiding edges for safety)
	var map_width = map_data.size()
	var map_height = map_data[0].size() if map_width > 0 else 0

	for x in range(0, map_width):
		for y in range(0, map_height):
			if not map_data[x][y]:  # false = water, true = island
				valid_positions.append(Vector2i(x, y))

	# If we found valid positions, select two that are far apart
	if valid_positions.size() >= 2:
		# Sort by distance from center to get spread out positions
		var center = Vector2i(map_width / 2, map_height / 2)
		valid_positions.sort_custom(func(a, b): return a.distance_to(center) < b.distance_to(center))

		# Return first (closest to center) and last (farthest from center) for good separation
		return [valid_positions[0], valid_positions[-1]]
	elif valid_positions.size() == 1:
		# Only one valid position found
		return [valid_positions[0], valid_positions[0]]
	else:
		# No valid positions found, use fallback
		print("Warning: No valid spawn positions found, using fallback positions")
		return [Vector2i(2, 2), Vector2i(12, 12)]

# Updates a submarine position and logs the move.
func process_captain_move(team_id: String, direction: String) -> void:
	if not teams.has(team_id):
		return
	var state: SubmarineState = teams[team_id]
	var delta := Vector2i.ZERO
	match direction:
		"NORTH":
			delta = Vector2i(0, -1)
		"SOUTH":
			delta = Vector2i(0, 1)
		"EAST":
			delta = Vector2i(1, 0)
		"WEST":
			delta = Vector2i(-1, 0)
		_:
			return

	# Calculate new position
	var new_position = state.position + delta

	# Get map dimensions
	var map_width = map_data.size() if map_data.size() > 0 else 15
	var map_height = map_data[0].size() if map_data.size() > 0 and map_data[0].size() > 0 else 15

	# Check boundaries (dynamic based on actual map size)
	if new_position.x < 0 or new_position.x >= map_width or new_position.y < 0 or new_position.y >= map_height:
		print("Cannot move %s - would go outside map boundaries!" % direction)
		return

	# Check for collision with islands (if map_data is available)
	if map_data.size() > 0 and new_position.x < map_data.size() and new_position.y < map_data[new_position.x].size():
		if map_data[new_position.x][new_position.y] == true:  # true = island
			print("Cannot move %s - would hit an island!" % direction)
			return

	# Update position only if valid
	state.position = new_position
	state.heading = direction
	state.move_log.append(direction)
	state.heat += 0.1
	emit_signal("submarine_moved", team_id, direction)
	emit_signal("state_updated", team_id)
