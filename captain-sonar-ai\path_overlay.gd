# PathOverlay.gd
extends Control

# The size of one tile in pixels. This MUST match your TileMap's tile size.
const TILE_SIZE = 64

# The list of moves. We'll add a function to update this from the outside.
var move_path: Array[String] = []

# A boolean to track if the mouse button is currently held down on this control.
var is_dragging = false

# --- Godot Functions ---

# This is a built-in function that is called automatically whenever the Control needs to be redrawn.
# We will tell it to redraw when the path changes or when it's moved.
func _draw():
	# Don't draw anything if there's no path.
	if move_path.is_empty():
		return

	# Start drawing from the local center of this control.
	var current_point = size / 2

	# Draw a small circle at the starting point.
	draw_circle(current_point, 4, Color.YELLOW)

	# Loop through each move in the path array.
	for move in move_path:
		var next_point = current_point
		match move:
			"NORTH":
				next_point.y -= TILE_SIZE
			"SOUTH":
				next_point.y += TILE_SIZE
			"EAST":
				next_point.x += TILE_SIZE
			"WEST":
				next_point.x -= TILE_SIZE
		
		# Draw a line from the current point to the next point.
		draw_line(current_point, next_point, Color.RED, 2.0)
		
		# The next point becomes our new current point for the next iteration.
		current_point = next_point


# This built-in function handles input events that occur over this Control node.
func _gui_input(event: InputEvent):
	# Check if the event is a mouse button press.
	if event is InputEventMouseButton:
		# If the left mouse button was pressed, start dragging.
		if event.button_index == MOUSE_BUTTON_LEFT and event.is_pressed():
			is_dragging = true
		# If the left mouse button was released, stop dragging.
		elif event.button_index == MOUSE_BUTTON_LEFT and not event.is_pressed():
			is_dragging = false
			
	# Check if the event is the mouse moving.
	if event is InputEventMouseMotion:
		# If we are currently dragging, move this entire control by the amount the mouse moved.
		if is_dragging:
			position += event.relative


# --- Custom Functions ---

# A public function we can call from our main RadioOperatorStation script
# to give this tool a new path to draw.
func set_new_path(new_path: Array[String]):
	move_path = new_path
	# This is crucial: queue_redraw() tells Godot to call the _draw() function again.
	queue_redraw()

# A function to clear the path.
func clear_path():
	move_path.clear()
	queue_redraw()
